import { render, screen } from '@testing-library/react';
import { ThemeProvider } from '@/components/ui/ThemeProvider';
import Home from '@/app/page';

// Mock framer-motion to avoid animation issues in tests
jest.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }: any) => <div {...props}>{children}</div>,
    section: ({ children, ...props }: any) => <section {...props}>{children}</section>,
    h1: ({ children, ...props }: any) => <h1 {...props}>{children}</h1>,
    p: ({ children, ...props }: any) => <p {...props}>{children}</p>,
    a: ({ children, ...props }: any) => <a {...props}>{children}</a>,
  },
}));

// Mock next/link
jest.mock('next/link', () => {
  return ({ children, href, ...props }: any) => (
    <a href={href} {...props}>
      {children}
    </a>
  );
});

// Mock next-themes
jest.mock('next-themes', () => ({
  ThemeProvider: ({ children }: any) => children,
  useTheme: () => ({
    theme: 'light',
    setTheme: jest.fn(),
  }),
}));

const renderWithTheme = (component: React.ReactElement) => {
  return render(
    <ThemeProvider attribute="class" defaultTheme="system" enableSystem>
      {component}
    </ThemeProvider>
  );
};

describe('Home Page', () => {
  it('renders hero section with name', () => {
    renderWithTheme(<Home />);
    expect(screen.getByText(/Hi, I'm/)).toBeInTheDocument();
    expect(screen.getByText(/Your Name/)).toBeInTheDocument();
  });

  it('renders hero description', () => {
    renderWithTheme(<Home />);
    expect(screen.getByText(/Full-stack developer passionate about creating/)).toBeInTheDocument();
  });

  it('renders call-to-action buttons', () => {
    renderWithTheme(<Home />);
    expect(screen.getByText(/View My Work/)).toBeInTheDocument();
    expect(screen.getByText(/Download Resume/)).toBeInTheDocument();
  });

  it('renders skills section', () => {
    renderWithTheme(<Home />);
    expect(screen.getByText(/Skills & Technologies/)).toBeInTheDocument();
  });

  it('renders featured projects section', () => {
    renderWithTheme(<Home />);
    expect(screen.getByText(/Featured Projects/)).toBeInTheDocument();
  });

  it('renders contact section', () => {
    renderWithTheme(<Home />);
    expect(screen.getByText(/Let's Work Together/)).toBeInTheDocument();
  });

  it('has proper navigation links', () => {
    renderWithTheme(<Home />);
    
    // Check for links to other pages
    const projectsLink = screen.getByText(/View My Work/);
    expect(projectsLink.closest('a')).toHaveAttribute('href', '/projects');
    
    const contactLink = screen.getByText(/Get In Touch/);
    expect(contactLink.closest('a')).toHaveAttribute('href', '/contact');
  });

  it('renders social media links', () => {
    renderWithTheme(<Home />);
    
    // Check for social media links (they should be present in the hero section)
    const socialLinks = screen.getAllByRole('link');
    const githubLink = socialLinks.find(link => 
      link.getAttribute('href')?.includes('github.com')
    );
    const linkedinLink = socialLinks.find(link => 
      link.getAttribute('href')?.includes('linkedin.com')
    );
    
    expect(githubLink).toBeInTheDocument();
    expect(linkedinLink).toBeInTheDocument();
  });
});
