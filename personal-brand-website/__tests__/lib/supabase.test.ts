import { db } from '@/lib/supabase';

// Mock Supabase client
jest.mock('@supabase/supabase-js', () => ({
  createClient: jest.fn(() => ({
    from: jest.fn(() => ({
      select: jest.fn(() => ({
        order: jest.fn(() => ({
          eq: jest.fn(() => ({
            data: mockProjects,
            error: null,
          })),
          data: mockProjects,
          error: null,
        })),
        eq: jest.fn(() => ({
          single: jest.fn(() => ({
            data: mockProjects[0],
            error: null,
          })),
          data: mockProjects,
          error: null,
        })),
        data: mockProjects,
        error: null,
      })),
      insert: jest.fn(() => ({
        select: jest.fn(() => ({
          single: jest.fn(() => ({
            data: mockContactMessage,
            error: null,
          })),
        })),
      })),
    })),
  })),
}));

const mockProjects = [
  {
    id: '1',
    title: 'Test Project',
    description: 'Test Description',
    technologies: ['React', 'TypeScript'],
    featured: true,
    created_at: '2024-01-01T00:00:00Z',
  },
];

const mockBlogPosts = [
  {
    id: '1',
    title: 'Test Blog Post',
    slug: 'test-blog-post',
    content: 'Test content',
    published: true,
    category: 'Technology',
    tags: ['React', 'Testing'],
    created_at: '2024-01-01T00:00:00Z',
  },
];

const mockContactMessage = {
  id: '1',
  name: 'John Doe',
  email: '<EMAIL>',
  message: 'Test message',
  created_at: '2024-01-01T00:00:00Z',
};

describe('Supabase Database Functions', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Projects', () => {
    it('should get all projects', async () => {
      const projects = await db.getProjects();
      expect(projects).toEqual(mockProjects);
    });

    it('should get featured projects only', async () => {
      const projects = await db.getProjects(true);
      expect(projects).toEqual(mockProjects);
    });

    it('should get a single project by id', async () => {
      const project = await db.getProject('1');
      expect(project).toEqual(mockProjects[0]);
    });
  });

  describe('Blog Posts', () => {
    it('should get published blog posts', async () => {
      // Mock the blog posts response
      const mockSupabase = require('@supabase/supabase-js');
      const mockClient = mockSupabase.createClient();
      mockClient.from.mockReturnValue({
        select: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            order: jest.fn().mockReturnValue({
              data: mockBlogPosts,
              error: null,
            }),
          }),
        }),
      });

      const posts = await db.getBlogPosts();
      expect(Array.isArray(posts)).toBe(true);
    });

    it('should get a blog post by slug', async () => {
      const mockSupabase = require('@supabase/supabase-js');
      const mockClient = mockSupabase.createClient();
      mockClient.from.mockReturnValue({
        select: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            eq: jest.fn().mockReturnValue({
              single: jest.fn().mockReturnValue({
                data: mockBlogPosts[0],
                error: null,
              }),
            }),
          }),
        }),
      });

      const post = await db.getBlogPost('test-blog-post');
      expect(post).toBeDefined();
    });

    it('should get blog posts by category', async () => {
      const mockSupabase = require('@supabase/supabase-js');
      const mockClient = mockSupabase.createClient();
      mockClient.from.mockReturnValue({
        select: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            eq: jest.fn().mockReturnValue({
              order: jest.fn().mockReturnValue({
                data: mockBlogPosts,
                error: null,
              }),
            }),
          }),
        }),
      });

      const posts = await db.getBlogPostsByCategory('Technology');
      expect(Array.isArray(posts)).toBe(true);
    });
  });

  describe('Contact Messages', () => {
    it('should create a contact message', async () => {
      const messageData = {
        name: 'John Doe',
        email: '<EMAIL>',
        message: 'Test message',
      };

      const result = await db.createContactMessage(messageData);
      expect(result).toEqual(mockContactMessage);
    });

    it('should handle contact message with subject', async () => {
      const messageData = {
        name: 'John Doe',
        email: '<EMAIL>',
        subject: 'Test Subject',
        message: 'Test message',
      };

      const result = await db.createContactMessage(messageData);
      expect(result).toBeDefined();
    });
  });

  describe('Error Handling', () => {
    it('should handle database errors gracefully', async () => {
      const mockSupabase = require('@supabase/supabase-js');
      const mockClient = mockSupabase.createClient();
      mockClient.from.mockReturnValue({
        select: jest.fn().mockReturnValue({
          order: jest.fn().mockReturnValue({
            data: null,
            error: { message: 'Database error' },
          }),
        }),
      });

      await expect(db.getProjects()).rejects.toEqual({
        message: 'Database error',
      });
    });
  });
});
