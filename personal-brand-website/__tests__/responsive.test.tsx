import { render, screen } from '@testing-library/react';
import { ThemeProvider } from '@/components/ui/ThemeProvider';
import Home from '@/app/page';

// Mock framer-motion
jest.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }: any) => <div {...props}>{children}</div>,
    section: ({ children, ...props }: any) => <section {...props}>{children}</section>,
    h1: ({ children, ...props }: any) => <h1 {...props}>{children}</h1>,
    p: ({ children, ...props }: any) => <p {...props}>{children}</p>,
    a: ({ children, ...props }: any) => <a {...props}>{children}</a>,
  },
}));

// Mock next/link
jest.mock('next/link', () => {
  return ({ children, href, ...props }: any) => (
    <a href={href} {...props}>
      {children}
    </a>
  );
});

// Mock next-themes
jest.mock('next-themes', () => ({
  ThemeProvider: ({ children }: any) => children,
  useTheme: () => ({
    theme: 'light',
    setTheme: jest.fn(),
  }),
}));

const renderWithTheme = (component: React.ReactElement) => {
  return render(
    <ThemeProvider attribute="class" defaultTheme="system" enableSystem>
      {component}
    </ThemeProvider>
  );
};

// Helper function to simulate different viewport sizes
const setViewport = (width: number, height: number) => {
  Object.defineProperty(window, 'innerWidth', {
    writable: true,
    configurable: true,
    value: width,
  });
  Object.defineProperty(window, 'innerHeight', {
    writable: true,
    configurable: true,
    value: height,
  });
  window.dispatchEvent(new Event('resize'));
};

describe('Responsive Design Tests', () => {
  beforeEach(() => {
    // Reset viewport to desktop size
    setViewport(1024, 768);
  });

  describe('Mobile Viewport (320px - 767px)', () => {
    beforeEach(() => {
      setViewport(375, 667); // iPhone SE size
    });

    it('renders mobile layout correctly', () => {
      renderWithTheme(<Home />);
      
      // Check that main content is present
      expect(screen.getByText(/Hi, I'm/)).toBeInTheDocument();
      expect(screen.getByText(/Your Name/)).toBeInTheDocument();
    });

    it('has proper mobile spacing and layout', () => {
      renderWithTheme(<Home />);
      
      // Check for responsive classes that should be applied
      const heroSection = screen.getByText(/Hi, I'm/).closest('section');
      expect(heroSection).toBeInTheDocument();
    });

    it('displays mobile-friendly navigation', () => {
      renderWithTheme(<Home />);
      
      // Navigation should be present (even if collapsed on mobile)
      const links = screen.getAllByRole('link');
      expect(links.length).toBeGreaterThan(0);
    });
  });

  describe('Tablet Viewport (768px - 1023px)', () => {
    beforeEach(() => {
      setViewport(768, 1024); // iPad size
    });

    it('renders tablet layout correctly', () => {
      renderWithTheme(<Home />);
      
      expect(screen.getByText(/Hi, I'm/)).toBeInTheDocument();
      expect(screen.getByText(/Skills & Technologies/)).toBeInTheDocument();
    });

    it('has appropriate tablet spacing', () => {
      renderWithTheme(<Home />);
      
      // Content should be properly spaced for tablet
      const skillsSection = screen.getByText(/Skills & Technologies/).closest('section');
      expect(skillsSection).toBeInTheDocument();
    });
  });

  describe('Desktop Viewport (1024px+)', () => {
    beforeEach(() => {
      setViewport(1440, 900); // Desktop size
    });

    it('renders desktop layout correctly', () => {
      renderWithTheme(<Home />);
      
      expect(screen.getByText(/Hi, I'm/)).toBeInTheDocument();
      expect(screen.getByText(/Skills & Technologies/)).toBeInTheDocument();
      expect(screen.getByText(/Featured Projects/)).toBeInTheDocument();
    });

    it('displays full navigation menu', () => {
      renderWithTheme(<Home />);
      
      // All navigation links should be visible on desktop
      const links = screen.getAllByRole('link');
      expect(links.length).toBeGreaterThan(5); // Should have multiple navigation links
    });

    it('has proper desktop grid layouts', () => {
      renderWithTheme(<Home />);
      
      // Check that grid layouts are working
      const projectsSection = screen.getByText(/Featured Projects/).closest('section');
      expect(projectsSection).toBeInTheDocument();
    });
  });

  describe('Large Desktop Viewport (1440px+)', () => {
    beforeEach(() => {
      setViewport(1920, 1080); // Large desktop
    });

    it('renders large desktop layout correctly', () => {
      renderWithTheme(<Home />);
      
      expect(screen.getByText(/Hi, I'm/)).toBeInTheDocument();
      expect(screen.getByText(/Let's Work Together/)).toBeInTheDocument();
    });

    it('maintains proper max-width constraints', () => {
      renderWithTheme(<Home />);
      
      // Content should not stretch too wide on large screens
      const heroSection = screen.getByText(/Hi, I'm/).closest('section');
      expect(heroSection).toBeInTheDocument();
    });
  });

  describe('Text Readability', () => {
    it('maintains readable text sizes across viewports', () => {
      const viewports = [
        [375, 667],   // Mobile
        [768, 1024],  // Tablet
        [1440, 900],  // Desktop
      ];

      viewports.forEach(([width, height]) => {
        setViewport(width, height);
        renderWithTheme(<Home />);
        
        // Main heading should be present and readable
        const heading = screen.getByText(/Hi, I'm/);
        expect(heading).toBeInTheDocument();
        
        // Description text should be present
        const description = screen.getByText(/Full-stack developer passionate/);
        expect(description).toBeInTheDocument();
      });
    });
  });

  describe('Interactive Elements', () => {
    it('maintains proper touch targets on mobile', () => {
      setViewport(375, 667);
      renderWithTheme(<Home />);
      
      // Buttons should be present and accessible
      const buttons = screen.getAllByRole('button');
      const links = screen.getAllByRole('link');
      
      expect(buttons.length + links.length).toBeGreaterThan(0);
    });

    it('has proper hover states on desktop', () => {
      setViewport(1440, 900);
      renderWithTheme(<Home />);
      
      // Interactive elements should be present
      const links = screen.getAllByRole('link');
      expect(links.length).toBeGreaterThan(0);
    });
  });

  describe('Content Hierarchy', () => {
    it('maintains proper content hierarchy across viewports', () => {
      const viewports = [
        [375, 667],   // Mobile
        [1440, 900],  // Desktop
      ];

      viewports.forEach(([width, height]) => {
        setViewport(width, height);
        renderWithTheme(<Home />);
        
        // Check that all main sections are present
        expect(screen.getByText(/Hi, I'm/)).toBeInTheDocument();
        expect(screen.getByText(/Skills & Technologies/)).toBeInTheDocument();
        expect(screen.getByText(/Featured Projects/)).toBeInTheDocument();
        expect(screen.getByText(/Let's Work Together/)).toBeInTheDocument();
      });
    });
  });
});
