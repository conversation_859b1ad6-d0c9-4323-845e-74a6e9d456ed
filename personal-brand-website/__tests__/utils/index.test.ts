import {
  cn,
  formatDate,
  formatRelativeTime,
  truncateText,
  generateSlug,
  isValidEmail,
  getReadingTime,
} from '@/utils';

describe('Utility Functions', () => {
  describe('cn (className utility)', () => {
    it('merges class names correctly', () => {
      expect(cn('class1', 'class2')).toBe('class1 class2');
    });

    it('handles conditional classes', () => {
      expect(cn('base', true && 'conditional', false && 'hidden')).toBe('base conditional');
    });

    it('handles undefined and null values', () => {
      expect(cn('base', undefined, null, 'end')).toBe('base end');
    });
  });

  describe('formatDate', () => {
    it('formats date string correctly', () => {
      const date = '2024-01-15T00:00:00Z';
      const formatted = formatDate(date);
      expect(formatted).toMatch(/January 15, 2024/);
    });

    it('formats Date object correctly', () => {
      const date = new Date('2024-01-15');
      const formatted = formatDate(date);
      expect(formatted).toMatch(/January 1[45], 2024/); // Account for timezone differences
    });
  });

  describe('formatRelativeTime', () => {
    it('returns "Just now" for very recent dates', () => {
      const now = new Date();
      expect(formatRelativeTime(now)).toBe('Just now');
    });

    it('returns correct format for days ago', () => {
      const threeDaysAgo = new Date(Date.now() - 3 * 24 * 60 * 60 * 1000);
      expect(formatRelativeTime(threeDaysAgo)).toBe('3 days ago');
    });

    it('returns correct format for hours ago', () => {
      const twoHoursAgo = new Date(Date.now() - 2 * 60 * 60 * 1000);
      expect(formatRelativeTime(twoHoursAgo)).toBe('2 hours ago');
    });
  });

  describe('truncateText', () => {
    it('truncates text longer than maxLength', () => {
      const text = 'This is a very long text that should be truncated';
      expect(truncateText(text, 20)).toBe('This is a very long...');
    });

    it('returns original text if shorter than maxLength', () => {
      const text = 'Short text';
      expect(truncateText(text, 20)).toBe('Short text');
    });

    it('handles exact length correctly', () => {
      const text = 'Exactly twenty chars';
      expect(truncateText(text, 20)).toBe('Exactly twenty chars');
    });
  });

  describe('generateSlug', () => {
    it('converts title to slug format', () => {
      expect(generateSlug('Hello World')).toBe('hello-world');
    });

    it('handles special characters', () => {
      expect(generateSlug('Hello, World! & More')).toBe('hello-world-more');
    });

    it('handles multiple spaces', () => {
      expect(generateSlug('Multiple   Spaces   Here')).toBe('multiple-spaces-here');
    });

    it('handles leading and trailing spaces', () => {
      expect(generateSlug('  Trimmed Text  ')).toBe('trimmed-text');
    });
  });

  describe('isValidEmail', () => {
    it('validates correct email addresses', () => {
      expect(isValidEmail('<EMAIL>')).toBe(true);
      expect(isValidEmail('<EMAIL>')).toBe(true);
      expect(isValidEmail('<EMAIL>')).toBe(true);
    });

    it('rejects invalid email addresses', () => {
      expect(isValidEmail('invalid-email')).toBe(false);
      expect(isValidEmail('@example.com')).toBe(false);
      expect(isValidEmail('user@')).toBe(false);
      expect(isValidEmail('user@domain')).toBe(false);
      expect(isValidEmail('')).toBe(false);
    });
  });

  describe('getReadingTime', () => {
    it('calculates reading time correctly', () => {
      const shortText = 'This is a short text with about ten words here.';
      expect(getReadingTime(shortText)).toBe(1); // Minimum 1 minute
    });

    it('calculates reading time for longer text', () => {
      const longText = Array(400).fill('word').join(' '); // 400 words
      expect(getReadingTime(longText)).toBe(2); // 400 words / 200 wpm = 2 minutes
    });

    it('handles empty text', () => {
      expect(getReadingTime('')).toBe(1); // Minimum 1 minute
    });
  });
});
