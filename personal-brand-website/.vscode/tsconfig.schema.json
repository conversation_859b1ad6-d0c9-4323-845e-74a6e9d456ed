{"$schema": "http://json-schema.org/draft-07/schema#", "title": "TypeScript Configuration", "description": "TypeScript configuration file schema", "type": "object", "properties": {"compilerOptions": {"type": "object", "description": "Instructs the TypeScript compiler how to compile .ts files", "properties": {"target": {"type": "string", "description": "Specify ECMAScript target version", "enum": ["ES3", "ES5", "ES6", "ES2015", "ES2016", "ES2017", "ES2018", "ES2019", "ES2020", "ES2021", "ES2022", "ESNext"]}, "lib": {"type": "array", "description": "List of library files to be included in the compilation", "items": {"type": "string"}}, "allowJs": {"type": "boolean", "description": "Allow JavaScript files to be compiled"}, "skipLibCheck": {"type": "boolean", "description": "Skip type checking of declaration files"}, "strict": {"type": "boolean", "description": "Enable all strict type checking options"}, "noEmit": {"type": "boolean", "description": "Do not emit outputs"}, "esModuleInterop": {"type": "boolean", "description": "Enables emit interoperability between CommonJS and ES Modules"}, "module": {"type": "string", "description": "Specify module code generation", "enum": ["none", "commonjs", "amd", "system", "umd", "es6", "es2015", "es2020", "esnext", "node16", "nodenext", "bundler"]}, "moduleResolution": {"type": "string", "description": "Specify module resolution strategy", "enum": ["node", "classic", "bundler"]}, "resolveJsonModule": {"type": "boolean", "description": "Include modules imported with .json extension"}, "isolatedModules": {"type": "boolean", "description": "Transpile each file as a separate module"}, "jsx": {"type": "string", "description": "Specify JSX code generation", "enum": ["preserve", "react", "react-jsx", "react-jsxdev", "react-native"]}, "incremental": {"type": "boolean", "description": "Enable incremental compilation"}, "plugins": {"type": "array", "description": "List of language service plugins to run inside the editor", "items": {"type": "object"}}, "paths": {"type": "object", "description": "A series of entries which re-map imports to lookup locations relative to the baseUrl"}, "types": {"type": "array", "description": "Type declaration files to be included in compilation", "items": {"type": "string"}}, "forceConsistentCasingInFileNames": {"type": "boolean", "description": "Disallow inconsistently-cased references to the same file"}, "allowSyntheticDefaultImports": {"type": "boolean", "description": "Allow default imports from modules with no default export"}}}, "include": {"type": "array", "description": "Specifies a list of glob patterns that match files to be included in compilation", "items": {"type": "string"}}, "exclude": {"type": "array", "description": "Specifies a list of files to be excluded from compilation", "items": {"type": "string"}}, "extends": {"type": "string", "description": "Path to base configuration file to inherit from"}}}