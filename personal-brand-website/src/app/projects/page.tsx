'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import Link from 'next/link';
import { ExternalLink, Github, Filter } from 'lucide-react';
import { Button } from '@/components/ui/Button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card';
import { Badge } from '@/components/ui/Badge';
import { db } from '@/lib/supabase';
import { Project } from '@/types';

// Mock data for development - replace with actual Supabase data
const mockProjects: Project[] = [
  {
    id: '1',
    title: 'E-commerce Platform',
    description: 'A full-stack e-commerce solution with modern UI/UX, payment integration, and admin dashboard.',
    long_description: 'This comprehensive e-commerce platform features a modern React frontend, Node.js backend, and PostgreSQL database. It includes user authentication, product management, shopping cart functionality, payment processing with Stripe, and a complete admin dashboard for managing orders and inventory.',
    image_url: '/projects/ecommerce.jpg',
    demo_url: 'https://ecommerce-demo.com',
    github_url: 'https://github.com/yourusername/ecommerce',
    technologies: ['Next.js', 'TypeScript', 'Stripe', 'PostgreSQL', 'Tailwind CSS'],
    featured: true,
    created_at: '2024-01-15T00:00:00Z',
    updated_at: '2024-01-15T00:00:00Z',
  },
  {
    id: '2',
    title: 'Task Management App',
    description: 'Collaborative task management with real-time updates, drag-and-drop interface, and team features.',
    long_description: 'A comprehensive task management application built for teams. Features include real-time collaboration, drag-and-drop task organization, team member management, project timelines, and detailed reporting. Built with React and Socket.io for real-time functionality.',
    image_url: '/projects/taskapp.jpg',
    demo_url: 'https://taskapp-demo.com',
    github_url: 'https://github.com/yourusername/taskapp',
    technologies: ['React', 'Node.js', 'Socket.io', 'MongoDB', 'Express.js'],
    featured: true,
    created_at: '2024-02-10T00:00:00Z',
    updated_at: '2024-02-10T00:00:00Z',
  },
  {
    id: '3',
    title: 'Weather Dashboard',
    description: 'Beautiful weather dashboard with forecasts, interactive maps, and location-based insights.',
    long_description: 'An elegant weather dashboard that provides detailed weather information, forecasts, and interactive maps. Features location-based weather data, historical weather patterns, and beautiful data visualizations using Chart.js.',
    image_url: '/projects/weather.jpg',
    demo_url: 'https://weather-demo.com',
    github_url: 'https://github.com/yourusername/weather',
    technologies: ['Vue.js', 'Express.js', 'Chart.js', 'Weather API', 'CSS3'],
    featured: false,
    created_at: '2024-03-05T00:00:00Z',
    updated_at: '2024-03-05T00:00:00Z',
  },
  {
    id: '4',
    title: 'Social Media Analytics',
    description: 'Analytics dashboard for social media performance tracking and insights.',
    long_description: 'A comprehensive analytics platform for tracking social media performance across multiple platforms. Includes data visualization, trend analysis, engagement metrics, and automated reporting features.',
    image_url: '/projects/analytics.jpg',
    demo_url: 'https://analytics-demo.com',
    github_url: 'https://github.com/yourusername/analytics',
    technologies: ['React', 'D3.js', 'Python', 'FastAPI', 'PostgreSQL'],
    featured: false,
    created_at: '2024-04-12T00:00:00Z',
    updated_at: '2024-04-12T00:00:00Z',
  },
];

const allTechnologies = Array.from(
  new Set(mockProjects.flatMap(project => project.technologies))
).sort();

export default function ProjectsPage() {
  const [projects, setProjects] = useState<Project[]>([]);
  const [filteredProjects, setFilteredProjects] = useState<Project[]>([]);
  const [selectedTech, setSelectedTech] = useState<string>('');
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // In a real app, fetch from Supabase
    // fetchProjects();
    
    // For now, use mock data
    setProjects(mockProjects);
    setFilteredProjects(mockProjects);
    setLoading(false);
  }, []);

  useEffect(() => {
    if (selectedTech) {
      setFilteredProjects(
        projects.filter(project => 
          project.technologies.includes(selectedTech)
        )
      );
    } else {
      setFilteredProjects(projects);
    }
  }, [selectedTech, projects]);

  const fetchProjects = async () => {
    try {
      const data = await db.getProjects();
      setProjects(data);
      setFilteredProjects(data);
    } catch (error) {
      console.error('Error fetching projects:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading projects...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen py-20 px-4 sm:px-6 lg:px-8">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <h1 className="text-4xl sm:text-5xl font-bold mb-4">My Projects</h1>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
            A collection of projects I've worked on, showcasing different 
            technologies and approaches to solving real-world problems.
          </p>
        </motion.div>

        {/* Filter */}
        <motion.div
          className="mb-12"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.1 }}
        >
          <div className="flex items-center gap-4 mb-4">
            <Filter className="h-5 w-5 text-muted-foreground" />
            <span className="font-medium">Filter by technology:</span>
          </div>
          <div className="flex flex-wrap gap-2">
            <Button
              variant={selectedTech === '' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setSelectedTech('')}
            >
              All
            </Button>
            {allTechnologies.map((tech) => (
              <Button
                key={tech}
                variant={selectedTech === tech ? 'default' : 'outline'}
                size="sm"
                onClick={() => setSelectedTech(tech)}
              >
                {tech}
              </Button>
            ))}
          </div>
        </motion.div>

        {/* Projects Grid */}
        <motion.div
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.5, delay: 0.2 }}
        >
          {filteredProjects.map((project, index) => (
            <motion.div
              key={project.id}
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
            >
              <Card className="h-full group hover:shadow-lg transition-shadow duration-300">
                <div className="relative overflow-hidden rounded-t-lg">
                  <div className="aspect-video bg-gradient-to-br from-primary/20 to-secondary/20 flex items-center justify-center">
                    <span className="text-muted-foreground">Project Image</span>
                  </div>
                  {project.featured && (
                    <Badge className="absolute top-4 left-4">Featured</Badge>
                  )}
                  <div className="absolute inset-0 bg-black/60 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center space-x-4">
                    {project.demo_url && (
                      <Button size="sm" variant="secondary" asChild>
                        <Link href={project.demo_url} target="_blank">
                          <ExternalLink className="h-4 w-4 mr-2" />
                          Demo
                        </Link>
                      </Button>
                    )}
                    {project.github_url && (
                      <Button size="sm" variant="secondary" asChild>
                        <Link href={project.github_url} target="_blank">
                          <Github className="h-4 w-4 mr-2" />
                          Code
                        </Link>
                      </Button>
                    )}
                  </div>
                </div>
                
                <CardHeader>
                  <CardTitle className="text-xl">{project.title}</CardTitle>
                  <CardDescription>{project.description}</CardDescription>
                </CardHeader>
                
                <CardContent>
                  <div className="flex flex-wrap gap-2 mb-4">
                    {project.technologies.map((tech) => (
                      <Badge key={tech} variant="outline" className="text-xs">
                        {tech}
                      </Badge>
                    ))}
                  </div>
                  <Button asChild className="w-full">
                    <Link href={`/projects/${project.id}`}>
                      View Details
                    </Link>
                  </Button>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </motion.div>

        {filteredProjects.length === 0 && (
          <motion.div
            className="text-center py-12"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5 }}
          >
            <p className="text-muted-foreground">
              No projects found with the selected technology.
            </p>
          </motion.div>
        )}
      </div>
    </div>
  );
}
