'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import Link from 'next/link';
import { useParams, useRouter } from 'next/navigation';
import { ArrowLeft, ExternalLink, Github, Calendar } from 'lucide-react';
import { Button } from '@/components/ui/Button';
import { Badge } from '@/components/ui/Badge';
import { db } from '@/lib/supabase';
import { Project } from '@/types';
import { formatDate } from '@/utils';

// Mock data for development
const mockProjects: Project[] = [
  {
    id: '1',
    title: 'E-commerce Platform',
    description: 'A full-stack e-commerce solution with modern UI/UX, payment integration, and admin dashboard.',
    long_description: 'This comprehensive e-commerce platform features a modern React frontend, Node.js backend, and PostgreSQL database. It includes user authentication, product management, shopping cart functionality, payment processing with Stripe, and a complete admin dashboard for managing orders and inventory.\n\nKey features include:\n- User registration and authentication\n- Product catalog with search and filtering\n- Shopping cart and checkout process\n- Payment integration with Stripe\n- Order management system\n- Admin dashboard for inventory management\n- Responsive design for all devices\n- SEO optimization\n\nThe platform is built with performance and scalability in mind, utilizing modern web technologies and best practices.',
    image_url: '/projects/ecommerce.jpg',
    demo_url: 'https://ecommerce-demo.com',
    github_url: 'https://github.com/yourusername/ecommerce',
    technologies: ['Next.js', 'TypeScript', 'Stripe', 'PostgreSQL', 'Tailwind CSS', 'Node.js', 'Express.js'],
    featured: true,
    created_at: '2024-01-15T00:00:00Z',
    updated_at: '2024-01-15T00:00:00Z',
  },
  {
    id: '2',
    title: 'Task Management App',
    description: 'Collaborative task management with real-time updates, drag-and-drop interface, and team features.',
    long_description: 'A comprehensive task management application built for teams. Features include real-time collaboration, drag-and-drop task organization, team member management, project timelines, and detailed reporting.\n\nKey features include:\n- Real-time collaboration with Socket.io\n- Drag-and-drop task organization\n- Team member management and permissions\n- Project timelines and milestones\n- File attachments and comments\n- Detailed reporting and analytics\n- Mobile-responsive design\n- Dark/light theme support\n\nBuilt with React and Socket.io for seamless real-time functionality.',
    image_url: '/projects/taskapp.jpg',
    demo_url: 'https://taskapp-demo.com',
    github_url: 'https://github.com/yourusername/taskapp',
    technologies: ['React', 'Node.js', 'Socket.io', 'MongoDB', 'Express.js', 'JWT'],
    featured: true,
    created_at: '2024-02-10T00:00:00Z',
    updated_at: '2024-02-10T00:00:00Z',
  },
];

export default function ProjectDetailPage() {
  const params = useParams();
  const router = useRouter();
  const [project, setProject] = useState<Project | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (params.id) {
      fetchProject(params.id as string);
    }
  }, [params.id]);

  const fetchProject = async (id: string) => {
    try {
      // In a real app, fetch from Supabase
      // const data = await db.getProject(id);
      
      // For now, use mock data
      const data = mockProjects.find(p => p.id === id);
      if (data) {
        setProject(data);
      } else {
        router.push('/projects');
      }
    } catch (error) {
      console.error('Error fetching project:', error);
      router.push('/projects');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading project...</p>
        </div>
      </div>
    );
  }

  if (!project) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4">Project Not Found</h1>
          <Button asChild>
            <Link href="/projects">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Projects
            </Link>
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen py-20 px-4 sm:px-6 lg:px-8">
      <div className="max-w-4xl mx-auto">
        {/* Back Button */}
        <motion.div
          className="mb-8"
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.5 }}
        >
          <Button variant="ghost" asChild>
            <Link href="/projects">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Projects
            </Link>
          </Button>
        </motion.div>

        {/* Project Header */}
        <motion.div
          className="mb-12"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.1 }}
        >
          <div className="flex items-center gap-4 mb-4">
            <h1 className="text-4xl sm:text-5xl font-bold">{project.title}</h1>
            {project.featured && (
              <Badge variant="default" className="text-sm">
                Featured
              </Badge>
            )}
          </div>
          
          <p className="text-xl text-muted-foreground mb-6">
            {project.description}
          </p>

          <div className="flex items-center gap-4 text-sm text-muted-foreground mb-8">
            <div className="flex items-center gap-1">
              <Calendar className="h-4 w-4" />
              <span>Created {formatDate(project.created_at)}</span>
            </div>
          </div>

          <div className="flex flex-wrap gap-4">
            {project.demo_url && (
              <Button asChild>
                <Link href={project.demo_url} target="_blank">
                  <ExternalLink className="h-4 w-4 mr-2" />
                  View Live Demo
                </Link>
              </Button>
            )}
            {project.github_url && (
              <Button variant="outline" asChild>
                <Link href={project.github_url} target="_blank">
                  <Github className="h-4 w-4 mr-2" />
                  View Source Code
                </Link>
              </Button>
            )}
          </div>
        </motion.div>

        {/* Project Image */}
        <motion.div
          className="mb-12"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
        >
          <div className="aspect-video bg-gradient-to-br from-primary/20 to-secondary/20 rounded-lg flex items-center justify-center">
            <span className="text-muted-foreground text-lg">Project Screenshot</span>
          </div>
        </motion.div>

        {/* Technologies */}
        <motion.div
          className="mb-12"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.3 }}
        >
          <h2 className="text-2xl font-bold mb-4">Technologies Used</h2>
          <div className="flex flex-wrap gap-2">
            {project.technologies.map((tech) => (
              <Badge key={tech} variant="secondary" className="text-sm">
                {tech}
              </Badge>
            ))}
          </div>
        </motion.div>

        {/* Project Description */}
        <motion.div
          className="mb-12"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.4 }}
        >
          <h2 className="text-2xl font-bold mb-6">About This Project</h2>
          <div className="prose prose-lg max-w-none text-muted-foreground">
            {project.long_description?.split('\n').map((paragraph, index) => (
              <p key={index} className="mb-4">
                {paragraph}
              </p>
            ))}
          </div>
        </motion.div>

        {/* Call to Action */}
        <motion.div
          className="text-center bg-accent/50 rounded-lg p-8"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.5 }}
        >
          <h3 className="text-2xl font-bold mb-4">Interested in Similar Work?</h3>
          <p className="text-muted-foreground mb-6">
            I'd love to discuss your project and how we can bring your ideas to life.
          </p>
          <Button asChild size="lg">
            <Link href="/contact">
              Get In Touch
            </Link>
          </Button>
        </motion.div>
      </div>
    </div>
  );
}
