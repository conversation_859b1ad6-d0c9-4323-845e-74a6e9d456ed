'use client';

import { motion } from 'framer-motion';
import Image from 'next/image';
import { Calendar, MapPin, Award, Users } from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card';
import { Badge } from '@/components/ui/Badge';

const experiences = [
  {
    id: '1',
    company: 'Tech Innovations Inc.',
    position: 'Senior Full-Stack Developer',
    startDate: '2022-01',
    endDate: null,
    current: true,
    location: 'San Francisco, CA',
    description: 'Lead development of scalable web applications using React, Node.js, and cloud technologies. Mentor junior developers and collaborate with cross-functional teams.',
    technologies: ['React', 'Node.js', 'TypeScript', 'AWS', 'PostgreSQL'],
  },
  {
    id: '2',
    company: 'Digital Solutions LLC',
    position: 'Frontend Developer',
    startDate: '2020-06',
    endDate: '2021-12',
    current: false,
    location: 'Remote',
    description: 'Developed responsive web applications and improved user experience across multiple client projects. Collaborated with designers and backend developers.',
    technologies: ['React', 'Vue.js', 'JavaScript', 'CSS3', 'REST APIs'],
  },
  {
    id: '3',
    company: 'StartupXYZ',
    position: 'Junior Developer',
    startDate: '2019-03',
    endDate: '2020-05',
    current: false,
    location: 'New York, NY',
    description: 'Built and maintained web applications, participated in code reviews, and learned modern development practices in a fast-paced startup environment.',
    technologies: ['JavaScript', 'Python', 'Django', 'HTML5', 'CSS3'],
  },
];

const education = [
  {
    id: '1',
    institution: 'University of Technology',
    degree: 'Bachelor of Science',
    field: 'Computer Science',
    startDate: '2015-09',
    endDate: '2019-05',
    description: 'Focused on software engineering, algorithms, and web development. Graduated Magna Cum Laude.',
  },
];

const achievements = [
  'Led a team of 5 developers to deliver a major project 2 weeks ahead of schedule',
  'Improved application performance by 40% through code optimization',
  'Mentored 10+ junior developers and interns',
  'Speaker at 3 tech conferences and meetups',
  'Open source contributor with 500+ GitHub stars across projects',
];

export default function AboutPage() {
  return (
    <div className="min-h-screen py-20 px-4 sm:px-6 lg:px-8">
      <div className="max-w-4xl mx-auto">
        {/* Hero Section */}
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <div className="relative w-32 h-32 mx-auto mb-8 rounded-full overflow-hidden bg-gradient-to-br from-primary/20 to-secondary/20">
            <div className="w-full h-full flex items-center justify-center text-muted-foreground">
              Photo
            </div>
          </div>
          <h1 className="text-4xl sm:text-5xl font-bold mb-4">About Me</h1>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
            I'm a passionate full-stack developer with over 5 years of experience 
            creating digital solutions that make a difference. I love turning 
            complex problems into simple, beautiful designs.
          </p>
        </motion.div>

        {/* Personal Story */}
        <motion.section
          className="mb-16"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.1 }}
        >
          <h2 className="text-3xl font-bold mb-6">My Story</h2>
          <div className="prose prose-lg max-w-none text-muted-foreground">
            <p className="mb-4">
              My journey into web development started during my college years when I built 
              my first website for a local business. The excitement of seeing my code come 
              to life in the browser sparked a passion that has only grown stronger over time.
            </p>
            <p className="mb-4">
              After graduating with a Computer Science degree, I dove headfirst into the 
              world of modern web development. I've had the privilege of working with 
              startups and established companies, each experience teaching me valuable 
              lessons about building scalable, user-focused applications.
            </p>
            <p>
              When I'm not coding, you can find me exploring new technologies, contributing 
              to open source projects, or sharing knowledge through blog posts and speaking 
              at tech meetups. I believe in continuous learning and staying curious about 
              the ever-evolving landscape of web development.
            </p>
          </div>
        </motion.section>

        {/* Experience */}
        <motion.section
          className="mb-16"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
        >
          <h2 className="text-3xl font-bold mb-8">Experience</h2>
          <div className="space-y-6">
            {experiences.map((exp, index) => (
              <motion.div
                key={exp.id}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.5, delay: 0.3 + index * 0.1 }}
              >
                <Card>
                  <CardHeader>
                    <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                      <div>
                        <CardTitle className="text-xl">{exp.position}</CardTitle>
                        <CardDescription className="text-lg font-medium text-primary">
                          {exp.company}
                        </CardDescription>
                      </div>
                      <div className="flex flex-col sm:items-end mt-2 sm:mt-0">
                        <div className="flex items-center text-sm text-muted-foreground mb-1">
                          <Calendar className="h-4 w-4 mr-1" />
                          {exp.startDate} - {exp.current ? 'Present' : exp.endDate}
                        </div>
                        <div className="flex items-center text-sm text-muted-foreground">
                          <MapPin className="h-4 w-4 mr-1" />
                          {exp.location}
                        </div>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <p className="text-muted-foreground mb-4">{exp.description}</p>
                    <div className="flex flex-wrap gap-2">
                      {exp.technologies.map((tech) => (
                        <Badge key={tech} variant="secondary">
                          {tech}
                        </Badge>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </motion.section>

        {/* Education */}
        <motion.section
          className="mb-16"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.4 }}
        >
          <h2 className="text-3xl font-bold mb-8">Education</h2>
          {education.map((edu) => (
            <Card key={edu.id}>
              <CardHeader>
                <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                  <div>
                    <CardTitle className="text-xl">
                      {edu.degree} in {edu.field}
                    </CardTitle>
                    <CardDescription className="text-lg font-medium text-primary">
                      {edu.institution}
                    </CardDescription>
                  </div>
                  <div className="flex items-center text-sm text-muted-foreground mt-2 sm:mt-0">
                    <Calendar className="h-4 w-4 mr-1" />
                    {edu.startDate} - {edu.endDate}
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">{edu.description}</p>
              </CardContent>
            </Card>
          ))}
        </motion.section>

        {/* Achievements */}
        <motion.section
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.5 }}
        >
          <h2 className="text-3xl font-bold mb-8">Key Achievements</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {achievements.map((achievement, index) => (
              <motion.div
                key={index}
                className="flex items-start space-x-3 p-4 rounded-lg bg-accent/50"
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.3, delay: 0.6 + index * 0.1 }}
              >
                <Award className="h-5 w-5 text-primary mt-0.5 flex-shrink-0" />
                <p className="text-sm">{achievement}</p>
              </motion.div>
            ))}
          </div>
        </motion.section>
      </div>
    </div>
  );
}
