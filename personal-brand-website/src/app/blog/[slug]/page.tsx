'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import Link from 'next/link';
import { useParams, useRouter } from 'next/navigation';
import { ArrowLeft, Calendar, Clock, Tag, Share2 } from 'lucide-react';
import { Button } from '@/components/ui/Button';
import { Badge } from '@/components/ui/Badge';
import { db } from '@/lib/supabase';
import { BlogPost } from '@/types';
import { formatDate, getReadingTime } from '@/utils';

// Mock data for development
const mockPosts: BlogPost[] = [
  {
    id: '1',
    title: 'Getting Started with Next.js 14',
    slug: 'getting-started-nextjs-14',
    excerpt: 'Learn how to build modern web applications with Next.js 14 and its latest features.',
    content: `# Getting Started with Next.js 14

Next.js 14 brings exciting new features and improvements that make building modern web applications even more enjoyable and efficient. In this comprehensive guide, we'll explore the key features and learn how to get started.

## What's New in Next.js 14

Next.js 14 introduces several groundbreaking features:

### App Router (Stable)
The App Router is now stable and provides a new way to build applications with React Server Components, nested layouts, and more.

### Server Actions
Server Actions allow you to run server-side code directly from your components, making form handling and data mutations simpler than ever.

### Turbopack (Beta)
Turbopack, the Rust-based bundler, is now available in beta and provides significantly faster build times.

## Installation

Getting started with Next.js 14 is straightforward:

\`\`\`bash
npx create-next-app@latest my-app
cd my-app
npm run dev
\`\`\`

## Key Features

### 1. React Server Components
Server Components allow you to render components on the server, reducing the JavaScript bundle size and improving performance.

### 2. Improved Performance
Next.js 14 includes numerous performance improvements, including faster builds and optimized runtime performance.

### 3. Enhanced Developer Experience
The developer experience has been significantly improved with better error messages, faster refresh, and more intuitive APIs.

## Best Practices

When working with Next.js 14, keep these best practices in mind:

1. **Use Server Components by default** - Only use Client Components when you need interactivity
2. **Optimize images** - Use the built-in Image component for automatic optimization
3. **Implement proper SEO** - Take advantage of Next.js's built-in SEO features
4. **Monitor performance** - Use the built-in analytics and monitoring tools

## Conclusion

Next.js 14 represents a significant step forward in React-based web development. With its focus on performance, developer experience, and modern web standards, it's an excellent choice for building production-ready applications.

Start exploring Next.js 14 today and experience the future of web development!`,
    image_url: '/blog/nextjs-14.jpg',
    published: true,
    category: 'Web Development',
    tags: ['Next.js', 'React', 'JavaScript', 'Tutorial'],
    created_at: '2024-01-15T00:00:00Z',
    updated_at: '2024-01-15T00:00:00Z',
    published_at: '2024-01-15T00:00:00Z',
  },
  {
    id: '2',
    title: 'Building Responsive Layouts with Tailwind CSS',
    slug: 'responsive-layouts-tailwind-css',
    excerpt: 'Master the art of creating beautiful, responsive layouts using Tailwind CSS utility classes.',
    content: `# Building Responsive Layouts with Tailwind CSS

Tailwind CSS has revolutionized how we approach styling in modern web development. Its utility-first approach makes creating responsive layouts both intuitive and efficient.

## The Mobile-First Approach

Tailwind CSS follows a mobile-first approach, which means you start designing for mobile devices and then add styles for larger screens.

\`\`\`html
<div class="w-full md:w-1/2 lg:w-1/3">
  <!-- Content -->
</div>
\`\`\`

## Responsive Breakpoints

Tailwind provides five default breakpoints:

- **sm**: 640px and up
- **md**: 768px and up  
- **lg**: 1024px and up
- **xl**: 1280px and up
- **2xl**: 1536px and up

## Grid Layouts

Creating responsive grid layouts is straightforward with Tailwind:

\`\`\`html
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
  <div>Item 1</div>
  <div>Item 2</div>
  <div>Item 3</div>
</div>
\`\`\`

## Flexbox Utilities

Flexbox is perfect for component-level layouts:

\`\`\`html
<div class="flex flex-col md:flex-row items-center justify-between">
  <div>Left content</div>
  <div>Right content</div>
</div>
\`\`\`

## Best Practices

1. **Start with mobile** - Always design mobile-first
2. **Use consistent spacing** - Stick to Tailwind's spacing scale
3. **Leverage component classes** - Extract common patterns into components
4. **Test on real devices** - Emulators are good, but real devices are better

## Conclusion

Tailwind CSS makes responsive design accessible and maintainable. By following these patterns and best practices, you'll be able to create beautiful, responsive layouts efficiently.`,
    image_url: '/blog/tailwind-css.jpg',
    published: true,
    category: 'CSS',
    tags: ['Tailwind CSS', 'CSS', 'Responsive Design', 'Frontend'],
    created_at: '2024-02-10T00:00:00Z',
    updated_at: '2024-02-10T00:00:00Z',
    published_at: '2024-02-10T00:00:00Z',
  },
];

export default function BlogPostPage() {
  const params = useParams();
  const router = useRouter();
  const [post, setPost] = useState<BlogPost | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (params.slug) {
      fetchPost(params.slug as string);
    }
  }, [params.slug]);

  const fetchPost = async (slug: string) => {
    try {
      // In a real app, fetch from Supabase
      // const data = await db.getBlogPost(slug);
      
      // For now, use mock data
      const data = mockPosts.find(p => p.slug === slug);
      if (data) {
        setPost(data);
      } else {
        router.push('/blog');
      }
    } catch (error) {
      console.error('Error fetching post:', error);
      router.push('/blog');
    } finally {
      setLoading(false);
    }
  };

  const sharePost = async () => {
    if (navigator.share && post) {
      try {
        await navigator.share({
          title: post.title,
          text: post.excerpt,
          url: window.location.href,
        });
      } catch (error) {
        console.log('Error sharing:', error);
      }
    } else {
      // Fallback: copy URL to clipboard
      navigator.clipboard.writeText(window.location.href);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading post...</p>
        </div>
      </div>
    );
  }

  if (!post) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4">Post Not Found</h1>
          <Button asChild>
            <Link href="/blog">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Blog
            </Link>
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen py-20 px-4 sm:px-6 lg:px-8">
      <div className="max-w-4xl mx-auto">
        {/* Back Button */}
        <motion.div
          className="mb-8"
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.5 }}
        >
          <Button variant="ghost" asChild>
            <Link href="/blog">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Blog
            </Link>
          </Button>
        </motion.div>

        {/* Post Header */}
        <motion.article
          className="mb-12"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.1 }}
        >
          <header className="mb-8">
            <Badge className="mb-4">{post.category}</Badge>
            <h1 className="text-4xl sm:text-5xl font-bold mb-4">{post.title}</h1>
            <p className="text-xl text-muted-foreground mb-6">{post.excerpt}</p>
            
            <div className="flex flex-wrap items-center gap-6 text-sm text-muted-foreground mb-6">
              <div className="flex items-center gap-1">
                <Calendar className="h-4 w-4" />
                <span>{formatDate(post.published_at || post.created_at)}</span>
              </div>
              <div className="flex items-center gap-1">
                <Clock className="h-4 w-4" />
                <span>{getReadingTime(post.content)} min read</span>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={sharePost}
                className="flex items-center gap-1"
              >
                <Share2 className="h-4 w-4" />
                Share
              </Button>
            </div>

            <div className="flex flex-wrap gap-2">
              {post.tags.map((tag) => (
                <Badge key={tag} variant="outline">
                  <Tag className="h-3 w-3 mr-1" />
                  {tag}
                </Badge>
              ))}
            </div>
          </header>

          {/* Post Image */}
          <div className="aspect-video bg-gradient-to-br from-primary/20 to-secondary/20 rounded-lg flex items-center justify-center mb-8">
            <span className="text-muted-foreground text-lg">Featured Image</span>
          </div>

          {/* Post Content */}
          <div className="prose prose-lg max-w-none">
            {post.content.split('\n').map((paragraph, index) => {
              if (paragraph.startsWith('# ')) {
                return (
                  <h1 key={index} className="text-3xl font-bold mt-8 mb-4">
                    {paragraph.replace('# ', '')}
                  </h1>
                );
              }
              if (paragraph.startsWith('## ')) {
                return (
                  <h2 key={index} className="text-2xl font-bold mt-6 mb-3">
                    {paragraph.replace('## ', '')}
                  </h2>
                );
              }
              if (paragraph.startsWith('### ')) {
                return (
                  <h3 key={index} className="text-xl font-bold mt-4 mb-2">
                    {paragraph.replace('### ', '')}
                  </h3>
                );
              }
              if (paragraph.startsWith('```')) {
                return (
                  <pre key={index} className="bg-accent p-4 rounded-lg overflow-x-auto my-4">
                    <code>{paragraph.replace(/```\w*\n?/, '').replace(/```$/, '')}</code>
                  </pre>
                );
              }
              if (paragraph.trim() === '') {
                return <br key={index} />;
              }
              return (
                <p key={index} className="mb-4 text-muted-foreground leading-relaxed">
                  {paragraph}
                </p>
              );
            })}
          </div>
        </motion.article>

        {/* Call to Action */}
        <motion.div
          className="text-center bg-accent/50 rounded-lg p-8"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.3 }}
        >
          <h3 className="text-2xl font-bold mb-4">Enjoyed This Post?</h3>
          <p className="text-muted-foreground mb-6">
            Check out more articles on web development, programming, and technology.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button asChild>
              <Link href="/blog">
                More Articles
              </Link>
            </Button>
            <Button variant="outline" asChild>
              <Link href="/contact">
                Get In Touch
              </Link>
            </Button>
          </div>
        </motion.div>
      </div>
    </div>
  );
}
