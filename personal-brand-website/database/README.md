# 数据库设置指南

本目录包含了个人品牌网站的数据库架构和设置脚本。

## 文件说明

- `setup.sql` - 完整的数据库设置脚本（推荐使用）
- `schema.sql` - 数据库表结构定义
- `triggers.sql` - 触发器定义
- `rls.sql` - 行级安全策略
- `seed.sql` - 示例数据

## 快速设置

1. 登录到您的 Supabase 项目控制台
2. 进入 SQL Editor
3. 复制并执行 `setup.sql` 中的内容

## 数据库表结构

### projects 表
存储项目信息，包括：
- 项目标题、描述
- 技术栈
- 演示链接和源码链接
- 是否为特色项目

### blog_posts 表
存储博客文章，包括：
- 文章标题、内容
- 分类和标签
- 发布状态
- SEO友好的slug

### contact_messages 表
存储联系表单提交的消息，包括：
- 发送者信息
- 消息内容
- 提交时间

## 安全策略

- 所有表都启用了行级安全策略（RLS）
- 公开读取已发布的内容
- 联系表单允许公开提交
- 管理功能需要认证

## 示例数据

运行 `seed.sql` 可以插入一些示例数据用于测试和开发。

## 环境变量

确保在 `.env.local` 文件中设置了正确的 Supabase 连接信息：

```
NEXT_PUBLIC_SUPABASE_URL=https://xixabumckpicevmxdxuo.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_anon_key
```
