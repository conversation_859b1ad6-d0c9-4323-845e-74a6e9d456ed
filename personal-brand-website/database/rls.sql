-- 启用行级安全策略
ALTER TABLE projects ENABLE ROW LEVEL SECURITY;
ALTER TABLE blog_posts ENABLE ROW LEVEL SECURITY;
ALTER TABLE contact_messages ENABLE ROW LEVEL SECURITY;

-- Projects表的RLS策略
-- 允许所有人查看项目
CREATE POLICY "Allow public read access on projects" ON projects
    FOR SELECT USING (true);

-- 只允许认证用户创建、更新、删除项目（管理员功能）
CREATE POLICY "Allow authenticated users to manage projects" ON projects
    FOR ALL USING (auth.role() = 'authenticated');

-- Blog posts表的RLS策略
-- 允许所有人查看已发布的博客文章
CREATE POLICY "Allow public read access on published blog posts" ON blog_posts
    FOR SELECT USING (published = true);

-- 允许所有人查看所有博客文章（包括草稿，用于管理界面）
CREATE POLICY "Allow public read access on all blog posts" ON blog_posts
    FOR SELECT USING (true);

-- 只允许认证用户管理博客文章
CREATE POLICY "Allow authenticated users to manage blog posts" ON blog_posts
    FOR ALL USING (auth.role() = 'authenticated');

-- Contact messages表的RLS策略
-- 允许所有人创建联系消息
CREATE POLICY "Allow public insert on contact messages" ON contact_messages
    FOR INSERT WITH CHECK (true);

-- 只允许认证用户查看联系消息
CREATE POLICY "Allow authenticated users to read contact messages" ON contact_messages
    FOR SELECT USING (auth.role() = 'authenticated');

-- 只允许认证用户删除联系消息
CREATE POLICY "Allow authenticated users to delete contact messages" ON contact_messages
    FOR DELETE USING (auth.role() = 'authenticated');
