-- 插入示例项目数据
INSERT INTO projects (title, description, long_description, technologies, featured, demo_url, github_url) VALUES
(
  'Personal Brand Website',
  'A modern, responsive personal brand website built with Next.js and Supabase',
  'This is a comprehensive personal brand website featuring a modern design, responsive layout, and powerful backend integration. Built with Next.js 14, TypeScript, Tailwind CSS, and Supabase for data management.',
  ARRAY['Next.js', 'TypeScript', 'Tailwind CSS', 'Supabase', 'Framer Motion'],
  true,
  'https://your-website.com',
  'https://github.com/yourusername/personal-brand-website'
),
(
  'E-commerce Platform',
  'Full-stack e-commerce solution with modern UI/UX',
  'A complete e-commerce platform featuring product management, shopping cart, payment integration, and admin dashboard. Built with modern technologies for optimal performance and user experience.',
  ARRAY['React', 'Node.js', 'MongoDB', 'Stripe', 'Redux'],
  true,
  'https://ecommerce-demo.com',
  'https://github.com/yourusername/ecommerce-platform'
),
(
  'Task Management App',
  'Collaborative task management application with real-time updates',
  'A productivity application that helps teams manage tasks, track progress, and collaborate effectively. Features real-time updates, drag-and-drop interface, and comprehensive reporting.',
  ARRAY['Vue.js', 'Express.js', 'PostgreSQL', 'Socket.io', 'Docker'],
  false,
  'https://taskapp-demo.com',
  'https://github.com/yourusername/task-management'
);

-- 插入示例博客文章数据
INSERT INTO blog_posts (title, slug, excerpt, content, category, tags, published, published_at) VALUES
(
  'Getting Started with Next.js 14',
  'getting-started-nextjs-14',
  'Learn how to build modern web applications with Next.js 14 and its latest features.',
  '# Getting Started with Next.js 14\n\nNext.js 14 brings exciting new features and improvements...\n\n## Key Features\n\n- App Router\n- Server Components\n- Improved Performance\n\n## Installation\n\n```bash\nnpx create-next-app@latest my-app\n```\n\nThis guide will walk you through the process of creating your first Next.js 14 application.',
  'Web Development',
  ARRAY['Next.js', 'React', 'JavaScript', 'Tutorial'],
  true,
  NOW() - INTERVAL '7 days'
),
(
  'Building Responsive Layouts with Tailwind CSS',
  'responsive-layouts-tailwind-css',
  'Master the art of creating beautiful, responsive layouts using Tailwind CSS utility classes.',
  '# Building Responsive Layouts with Tailwind CSS\n\nTailwind CSS makes it easy to create responsive designs...\n\n## Mobile-First Approach\n\nTailwind follows a mobile-first approach to responsive design.\n\n```html\n<div class="w-full md:w-1/2 lg:w-1/3">\n  <!-- Content -->\n</div>\n```',
  'CSS',
  ARRAY['Tailwind CSS', 'CSS', 'Responsive Design', 'Frontend'],
  true,
  NOW() - INTERVAL '14 days'
),
(
  'Database Design Best Practices',
  'database-design-best-practices',
  'Essential principles and patterns for designing efficient and scalable databases.',
  '# Database Design Best Practices\n\nGood database design is crucial for application performance...\n\n## Normalization\n\nNormalization helps eliminate data redundancy and improve data integrity.\n\n## Indexing Strategies\n\nProper indexing can significantly improve query performance.',
  'Database',
  ARRAY['Database', 'SQL', 'PostgreSQL', 'Best Practices'],
  false,
  NULL
);
